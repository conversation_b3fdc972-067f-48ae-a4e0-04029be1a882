import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'md',
      isLoading = false,
      fullWidth = false,
      className = '',
      icon,
      ...props
    },
    ref
  ) => {
    const baseClasses = 'inline-flex items-center justify-center rounded-xl font-bold transition-all duration-500 focus:outline-none focus:ring-4 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform-gpu hover:scale-105 active:scale-95';
    
    const variantClasses = {
      primary: 'bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-neutral-900 hover:from-yellow-500 hover:via-yellow-600 hover:to-yellow-700 focus:ring-yellow-400 shadow-lg hover:shadow-2xl border-b-4 border-yellow-700 hover:border-yellow-800',
      secondary: 'bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 text-white hover:from-blue-700 hover:via-blue-800 hover:to-blue-900 focus:ring-blue-500 shadow-lg hover:shadow-2xl border-b-4 border-blue-900 hover:border-blue-950',
      outline: 'border-3 border-yellow-400 text-yellow-600 hover:bg-gradient-to-r hover:from-yellow-400 hover:to-yellow-500 hover:text-neutral-900 focus:ring-yellow-400 shadow-lg hover:shadow-2xl bg-white/10 backdrop-blur-sm',
      ghost: 'text-neutral-700 hover:text-neutral-900 hover:bg-neutral-200/50 focus:ring-neutral-400 bg-transparent shadow-none hover:shadow-md',
    };
    
    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
    };
    
    const widthClass = fullWidth ? 'w-full' : '';
    
    const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`;
    
    return (
      <button
        ref={ref}
        className={classes}
        disabled={isLoading || props.disabled}
        {...props}
      >
        {isLoading ? (
          <>
            <span className="absolute inset-0 flex items-center justify-center">
              <span className="paw-loader animate-spin">🐾</span>
            </span>
            <span className="opacity-0">{children}</span>
          </>
        ) : (
          <>
            {icon && <span className="mr-2">{icon}</span>}
            {children}
          </>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
