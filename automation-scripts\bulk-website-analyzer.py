#!/usr/bin/env python3
"""
Bulk Website SEO & Performance Analyzer
Analyzes multiple websites for SEO issues, performance, and technical problems
"""

import requests
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import json
import csv
from datetime import datetime
import concurrent.futures
import ssl
import socket

class WebsiteAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def analyze_website(self, url):
        """Complete website analysis"""
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        analysis = {
            'url': url,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'success',
            'errors': []
        }
        
        try:
            # Basic request
            start_time = time.time()
            response = self.session.get(url, timeout=30, allow_redirects=True)
            load_time = time.time() - start_time
            
            analysis.update({
                'status_code': response.status_code,
                'load_time': round(load_time, 2),
                'final_url': response.url,
                'redirects': len(response.history)
            })
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                analysis.update(self._analyze_seo(soup))
                analysis.update(self._analyze_technical(response, soup))
                analysis.update(self._analyze_performance(response))
            else:
                analysis['errors'].append(f"HTTP {response.status_code}")
                
        except Exception as e:
            analysis['status'] = 'error'
            analysis['errors'].append(str(e))
            
        return analysis
    
    def _analyze_seo(self, soup):
        """SEO analysis"""
        seo = {}
        
        # Title
        title = soup.find('title')
        seo['title'] = title.text.strip() if title else ''
        seo['title_length'] = len(seo['title'])
        seo['title_issues'] = []
        
        if not seo['title']:
            seo['title_issues'].append('Missing title')
        elif seo['title_length'] < 30:
            seo['title_issues'].append('Title too short')
        elif seo['title_length'] > 60:
            seo['title_issues'].append('Title too long')
            
        # Meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        seo['meta_description'] = meta_desc.get('content', '') if meta_desc else ''
        seo['meta_desc_length'] = len(seo['meta_description'])
        seo['meta_desc_issues'] = []
        
        if not seo['meta_description']:
            seo['meta_desc_issues'].append('Missing meta description')
        elif seo['meta_desc_length'] < 120:
            seo['meta_desc_issues'].append('Meta description too short')
        elif seo['meta_desc_length'] > 160:
            seo['meta_desc_issues'].append('Meta description too long')
            
        # Headings
        h1_tags = soup.find_all('h1')
        seo['h1_count'] = len(h1_tags)
        seo['h1_text'] = [h1.get_text().strip() for h1 in h1_tags]
        
        seo['heading_structure'] = {
            'h1': len(soup.find_all('h1')),
            'h2': len(soup.find_all('h2')),
            'h3': len(soup.find_all('h3')),
            'h4': len(soup.find_all('h4')),
            'h5': len(soup.find_all('h5')),
            'h6': len(soup.find_all('h6'))
        }
        
        # Images
        images = soup.find_all('img')
        seo['total_images'] = len(images)
        seo['images_without_alt'] = len([img for img in images if not img.get('alt')])
        
        # Links
        links = soup.find_all('a', href=True)
        seo['total_links'] = len(links)
        seo['external_links'] = len([link for link in links if self._is_external_link(link.get('href'))])
        
        return seo
    
    def _analyze_technical(self, response, soup):
        """Technical SEO analysis"""
        tech = {}
        
        # SSL
        tech['https'] = response.url.startswith('https://')
        
        # Canonical
        canonical = soup.find('link', rel='canonical')
        tech['canonical'] = canonical.get('href') if canonical else ''
        
        # Robots meta
        robots_meta = soup.find('meta', attrs={'name': 'robots'})
        tech['robots_meta'] = robots_meta.get('content', '') if robots_meta else ''
        
        # Open Graph
        og_tags = soup.find_all('meta', attrs={'property': lambda x: x and x.startswith('og:')})
        tech['og_tags_count'] = len(og_tags)
        
        # Schema markup
        schema_scripts = soup.find_all('script', type='application/ld+json')
        tech['schema_markup'] = len(schema_scripts) > 0
        
        # Viewport
        viewport = soup.find('meta', attrs={'name': 'viewport'})
        tech['viewport'] = viewport.get('content', '') if viewport else ''
        tech['mobile_friendly'] = 'viewport' in str(viewport) if viewport else False
        
        return tech
    
    def _analyze_performance(self, response):
        """Performance analysis"""
        perf = {}
        
        # Response size
        perf['response_size_kb'] = round(len(response.content) / 1024, 2)
        
        # Headers analysis
        headers = response.headers
        perf['gzip_enabled'] = 'gzip' in headers.get('content-encoding', '')
        perf['cache_control'] = headers.get('cache-control', '')
        perf['server'] = headers.get('server', '')
        
        return perf
    
    def _is_external_link(self, href):
        """Check if link is external"""
        if not href or href.startswith(('#', 'mailto:', 'tel:')):
            return False
        return href.startswith(('http://', 'https://'))
    
    def bulk_analyze(self, urls, output_file='website_analysis.csv', max_workers=5):
        """Analyze multiple websites concurrently"""
        results = []
        
        print(f"Analyzing {len(urls)} websites...")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {executor.submit(self.analyze_website, url): url for url in urls}
            
            for i, future in enumerate(concurrent.futures.as_completed(future_to_url), 1):
                url = future_to_url[future]
                try:
                    result = future.result()
                    results.append(result)
                    print(f"Completed {i}/{len(urls)}: {url}")
                except Exception as e:
                    print(f"Error analyzing {url}: {e}")
                    results.append({
                        'url': url,
                        'status': 'error',
                        'errors': [str(e)],
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
        
        # Save results
        self._save_results(results, output_file)
        return results
    
    def _save_results(self, results, output_file):
        """Save analysis results to CSV"""
        if not results:
            return
            
        # Flatten nested dictionaries for CSV
        flattened_results = []
        for result in results:
            flat_result = {}
            for key, value in result.items():
                if isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        flat_result[f"{key}_{sub_key}"] = sub_value
                elif isinstance(value, list):
                    flat_result[key] = '; '.join(map(str, value))
                else:
                    flat_result[key] = value
            flattened_results.append(flat_result)
        
        # Write to CSV
        if flattened_results:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = flattened_results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(flattened_results)
            
            print(f"Analysis complete! Results saved to {output_file}")

# Usage example
if __name__ == "__main__":
    analyzer = WebsiteAnalyzer()
    
    # Example URLs to analyze
    urls = [
        "example.com",
        "google.com", 
        "github.com",
        "stackoverflow.com"
    ]
    
    results = analyzer.bulk_analyze(urls)
    
    # Print summary
    print("\nAnalysis Summary:")
    for result in results:
        if result['status'] == 'success':
            print(f"✓ {result['url']} - Load time: {result.get('load_time', 'N/A')}s")
        else:
            print(f"✗ {result['url']} - Error: {'; '.join(result.get('errors', []))}")
