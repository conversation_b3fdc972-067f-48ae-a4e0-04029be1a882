'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

const ModernHeader: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const categories = [
    {
      title: 'Qentë',
      icon: '🐕',
      subcategories: ['Ushqim për Qentë', 'Lodra për Qentë', 'Aksesorë për Qentë', 'Kujdes Shëndetësor']
    },
    {
      title: '<PERSON><PERSON>',
      icon: '🐱',
      subcategories: ['<PERSON>hqim për Mace', '<PERSON><PERSON> për Mace', '<PERSON><PERSON><PERSON><PERSON><PERSON> për <PERSON>', '<PERSON>j<PERSON>hëndetësor']
    },
    {
      title: 'Zogjtë',
      icon: '🐦',
      subcategories: ['Ushqim për Zogj', 'Kafaze', 'Aksesorë për Zogj', 'Kujdes Shëndetësor']
    },
    {
      title: 'Peshqit',
      icon: '🐠',
      subcategories: ['Ushqim për Peshq', 'Akuariumë', 'Dekorime', 'Pajisje Teknike']
    }
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
      isScrolled
        ? 'bg-white/98 backdrop-blur-xl shadow-2xl border-b border-neutral-200/50'
        : 'bg-white/95 backdrop-blur-lg border-b border-neutral-100/30'
    }`}>
      {/* Top Bar */}
      <div className="bg-gradient-to-r from-neutral-900 to-neutral-800 text-white py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-6">
              <span className="flex items-center">
                <span className="mr-2">📞</span>
                +355 69 123 4567
              </span>
              <span className="flex items-center">
                <span className="mr-2">📧</span>
                <EMAIL>
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <span className="mr-2">🚚</span>
                Transport Falas mbi 5,000 Lekë
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            {/* Clean Simple Logo */}
            <Link href="/" className="flex items-center space-x-3 no-underline hover:no-underline" style={{ textDecoration: 'none' }}>
              <div className="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">🐾</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-neutral-900">
                  PetShop<span className="text-primary-500">AL</span>
                </h1>
              </div>
            </Link>

            {/* Modern Search Bar - Desktop */}
            <div className="hidden lg:flex flex-1 max-w-3xl mx-8">
              <div className="relative w-full group">
                <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
                  <svg className="h-6 w-6 text-neutral-400 group-focus-within:text-primary-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Gjej produktet më të mira për kafshën tënde..."
                  className="w-full pl-14 pr-16 py-4 bg-gradient-to-r from-neutral-50 to-neutral-100 border-2 border-neutral-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500 focus:bg-white transition-all duration-300 text-base placeholder-neutral-500 shadow-sm hover:shadow-md"
                />
                <div className="absolute inset-y-0 right-0 pr-2 flex items-center">
                  <button className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-6 py-2 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            {/* Modern Action Icons */}
            <div className="flex items-center space-x-3">
              {/* Search Icon - Mobile */}
              <button className="lg:hidden p-3 text-neutral-600 hover:text-primary-600 transition-all duration-300 hover:bg-neutral-100 rounded-xl">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>

              {/* User Account */}
              <Link href="/account" className="hidden sm:flex p-3 text-neutral-600 hover:text-primary-600 transition-all duration-300 hover:bg-neutral-100 rounded-xl group" aria-label="Llogaria">
                <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </Link>

              {/* Wishlist */}
              <Link href="/wishlist" className="hidden sm:flex p-3 text-neutral-600 hover:text-primary-600 relative transition-all duration-300 hover:bg-neutral-100 rounded-xl group" aria-label="Lista e Dëshirave">
                <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse font-bold">
                  2
                </span>
            </Link>

              {/* Cart */}
              <Link href="/cart" className="p-3 text-neutral-600 hover:text-primary-600 relative transition-all duration-300 hover:bg-neutral-100 rounded-xl group" aria-label="Shporta">
                <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                <span className="absolute -top-1 -right-1 bg-gradient-to-r from-primary-500 to-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse font-bold shadow-lg">
                  3
                </span>
              </Link>

              {/* Mobile Menu Button */}
              <button
                className="lg:hidden p-3 text-neutral-600 hover:text-primary-600 transition-all duration-300 hover:bg-neutral-100 rounded-xl"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                aria-label={isMenuOpen ? "Mbyll menunë" : "Hap menunë"}
              >
                {isMenuOpen ? (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Navigation Menu - Desktop */}
      <nav className="hidden lg:block bg-gradient-to-r from-neutral-50 to-neutral-100 border-t border-neutral-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center space-x-12 py-4">
            <Link href="/" className="flex items-center space-x-2 text-neutral-700 hover:text-primary-600 font-semibold transition-all duration-300 relative group px-4 py-2 rounded-xl hover:bg-white/80">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              <span>Faqja Kryesore</span>
            </Link>

            <Link href="/products" className="flex items-center space-x-2 text-neutral-700 hover:text-primary-600 font-semibold transition-all duration-300 relative group px-4 py-2 rounded-xl hover:bg-white/80">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              <span>Produktet</span>
            </Link>

            {/* Categories with Modern Megamenu */}
            <div className="relative group">
              <button
                className="flex items-center space-x-2 text-neutral-700 hover:text-primary-600 font-semibold transition-all duration-300 relative group px-4 py-2 rounded-xl hover:bg-white/80"
                onMouseEnter={() => setIsCategoriesOpen(true)}
                onMouseLeave={() => setIsCategoriesOpen(false)}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <span>Kategoritë</span>
                <svg className={`w-4 h-4 transition-transform duration-300 ${isCategoriesOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Modern Megamenu */}
              <div
                className={`absolute top-full left-1/2 transform -translate-x-1/2 mt-4 w-[500px] bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl border border-neutral-200 transition-all duration-300 ${
                  isCategoriesOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible translate-y-2'
                }`}
                onMouseEnter={() => setIsCategoriesOpen(true)}
                onMouseLeave={() => setIsCategoriesOpen(false)}
              >
                <div className="p-6">
                  <div className="grid grid-cols-2 gap-4">
                    {categories.map((category, index) => (
                      <div key={index} className="space-y-3">
                        <div className="flex items-center space-x-3 pb-2 border-b border-neutral-100">
                          <span className="text-2xl">{category.icon}</span>
                          <h3 className="font-semibold text-neutral-900">{category.title}</h3>
                        </div>
                        <div className="space-y-2">
                          {category.subcategories.map((sub, subIndex) => (
                            <Link
                              key={subIndex}
                              href={`/categories/${category.title.toLowerCase()}/${sub.toLowerCase().replace(/\s+/g, '-')}`}
                              className="block text-sm text-neutral-600 hover:text-primary-600 transition-colors duration-200 hover:bg-neutral-50 rounded px-2 py-1"
                            >
                              {sub}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-6 pt-4 border-t border-neutral-200">
                    <Link
                      href="/categories"
                      className="block text-center bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                    >
                      Shiko të gjitha kategoritë
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            <Link href="/rreth-nesh" className="flex items-center space-x-2 text-neutral-700 hover:text-primary-600 font-semibold transition-all duration-300 relative group px-4 py-2 rounded-xl hover:bg-white/80">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Rreth Nesh</span>
            </Link>

            <Link href="/kontakt" className="flex items-center space-x-2 text-neutral-700 hover:text-primary-600 font-semibold transition-all duration-300 relative group px-4 py-2 rounded-xl hover:bg-white/80">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span>Kontakt</span>
            </Link>
          </div>
        </div>
      </nav>


        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 pb-4 border-t border-neutral-200 animate-fade-in">
            {/* Mobile Search */}
            <div className="relative my-4">
              <input
                type="text"
                placeholder="Kërko produkte..."
                className="w-full pl-4 pr-10 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
              />
              <button className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-neutral-500 hover:text-primary-600 transition-all duration-300">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>

            {/* Mobile Navigation Links */}
            <nav className="space-y-2">
              <Link
                href="/"
                className="flex items-center space-x-3 py-3 px-4 text-neutral-700 hover:bg-neutral-100 rounded-lg transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span>Faqja Kryesore</span>
              </Link>

              <Link
                href="/products"
                className="flex items-center space-x-3 py-3 px-4 text-neutral-700 hover:bg-neutral-100 rounded-lg transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                <span>Produktet</span>
              </Link>

              {/* Mobile Categories with Expandable Menu */}
              <div className="py-2 px-4 hover:bg-neutral-100 rounded-lg transition-all duration-200">
                <button
                  className="flex items-center justify-between w-full py-2 text-neutral-700"
                  onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
                >
                  <div className="flex items-center space-x-3">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <span>Kategoritë</span>
                  </div>
                  <svg
                    className={`w-4 h-4 transition-transform duration-300 ${isCategoriesOpen ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isCategoriesOpen && (
                  <div className="mt-2 pl-4 space-y-2 border-l-2 border-primary-200 animate-fade-in">
                    {categories.map((category, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center space-x-2 py-2 text-neutral-800 font-medium">
                          <span>{category.icon}</span>
                          <span>{category.title}</span>
                        </div>
                        <div className="pl-6 space-y-1">
                          {category.subcategories.map((sub, subIndex) => (
                            <Link
                              key={subIndex}
                              href={`/categories/${category.title.toLowerCase()}/${sub.toLowerCase().replace(/\s+/g, '-')}`}
                              className="block py-1 text-sm text-neutral-600 hover:text-primary-600 transition-all duration-200"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              {sub}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                    <Link
                      href="/categories"
                      className="block py-2 text-primary-600 font-medium transition-all duration-200 hover:bg-primary-50 rounded-lg mt-3"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Shiko të gjitha kategoritë
                    </Link>
                  </div>
                )}
              </div>

              <Link
                href="/rreth-nesh"
                className="flex items-center space-x-3 py-3 px-4 text-neutral-700 hover:bg-neutral-100 rounded-lg transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Rreth Nesh</span>
              </Link>

              <Link
                href="/kontakt"
                className="flex items-center space-x-3 py-3 px-4 text-neutral-700 hover:bg-neutral-100 rounded-lg transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>Kontakt</span>
              </Link>
            </nav>

            {/* Mobile Action Buttons */}
            <div className="mt-6 pt-4 border-t border-neutral-200 grid grid-cols-3 gap-2">
              <Link
                href="/wishlist"
                className="flex flex-col items-center p-2 text-neutral-600 hover:text-primary-600 transition-all duration-200 hover:bg-neutral-100 rounded-lg"
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="relative">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  <span className="absolute -top-2 -right-2 bg-primary-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center animate-pulse">
                    2
                  </span>
                </div>
                <span className="text-xs mt-1">Dëshirat</span>
              </Link>

              <Link
                href="/cart"
                className="flex flex-col items-center p-2 text-neutral-600 hover:text-primary-600 transition-all duration-200 hover:bg-neutral-100 rounded-lg"
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="relative">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                  <span className="absolute -top-2 -right-2 bg-primary-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center animate-pulse">
                    3
                  </span>
                </div>
                <span className="text-xs mt-1">Shporta</span>
              </Link>

              <Link
                href="/account"
                className="flex flex-col items-center p-2 text-neutral-600 hover:text-primary-600 transition-all duration-200 hover:bg-neutral-100 rounded-lg"
                onClick={() => setIsMenuOpen(false)}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span className="text-xs mt-1">Llogaria</span>
              </Link>
            </div>
          </div>
        )}
    </header>
  );
};

export default ModernHeader;
