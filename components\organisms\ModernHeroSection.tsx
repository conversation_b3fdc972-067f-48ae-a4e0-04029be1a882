'use client';

import React from 'react';
import Link from 'next/link';

const ModernHeroSection: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800"
             style={{
               position: 'relative'
             }}>
      {/* Enhanced Animated Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 animate-float-pattern opacity-30"
             style={{
               backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='20' cy='20' r='3' fill='rgba(255,224,3,0.2)'/%3E%3Ccircle cx='80' cy='40' r='2' fill='rgba(255,224,3,0.25)'/%3E%3Ccircle cx='40' cy='80' r='1.5' fill='rgba(255,224,3,0.2)'/%3E%3Ccircle cx='60' cy='20' r='2.5' fill='rgba(255,255,255,0.1)'/%3E%3C/svg%3E")`,
               backgroundRepeat: 'repeat',
               backgroundSize: '100px 100px'
             }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-1/4 left-1/4 w-16 h-16 rounded-full bg-yellow-400/20 blur-xl animate-float"></div>
      <div className="absolute bottom-1/3 right-1/4 w-24 h-24 rounded-full bg-purple-400/20 blur-xl animate-float animation-delay-2000"></div>
      <div className="absolute top-1/2 right-1/3 w-20 h-20 rounded-full bg-blue-400/20 blur-xl animate-float animation-delay-3000"></div>

      {/* Main Content Container */}
      <div className="container-custom relative z-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          
          {/* Enhanced Hero Content */}
          <div className="text-center lg:text-left animate-fade-in">
            {/* Enhanced Trust Badge with Glass Effect */}
            <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-md border-2 border-white/30 text-white rounded-full text-sm font-bold mb-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <span className="mr-2 animate-pulse">🌟</span>
              E Veçantë Sot
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-primary font-bold mb-8 leading-tight text-white animate-slide-in-left">
              Produkte{' '}
              <span className="bg-gradient-to-r from-yellow-300 via-yellow-400 to-yellow-500 bg-clip-text text-transparent animate-pulse">
                Premium
              </span>{' '}
              për Kafshë
            </h1>

            <p className="text-lg sm:text-xl text-white/90 mb-10 leading-relaxed max-w-2xl animate-slide-in-left animation-delay-200">
              Zbuloni koleksionin tonë të kujdesshëm të furnizimeve të cilësisë së lartë për kafshë,
              lodrave dhe aksesorëve të dizajnuara për t'i mbajtur miqtë tuaj me gëzof të lumtur dhe të shëndetshëm.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 mb-10 animate-slide-in-left animation-delay-400">
              <Link href="/products" className="group inline-flex items-center justify-center px-10 py-4 text-lg font-bold rounded-full transition-all duration-500 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-neutral-900 shadow-lg hover:shadow-2xl transform hover:-translate-y-2 hover:scale-105">
                <span>Blej Tani</span>
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
              <Link href="/about" className="group inline-flex items-center justify-center px-10 py-4 text-lg font-bold rounded-full transition-all duration-500 bg-white/20 backdrop-blur-md text-white border-2 border-white/30 shadow-lg hover:shadow-2xl hover:bg-white/30 transform hover:-translate-y-2 hover:scale-105">
                <span>Mëso Më Shumë</span>
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
            
            {/* Enhanced Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 border-t border-white/20 animate-fade-in animation-delay-600">
              <div className="text-center group">
                <div className="text-3xl font-bold text-yellow-400 mb-2 group-hover:scale-110 transition-transform duration-300">1500+</div>
                <div className="text-sm text-white/80 font-medium">Produkte të Cilësisë</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl font-bold text-blue-400 mb-2 group-hover:scale-110 transition-transform duration-300">24/7</div>
                <div className="text-sm text-white/80 font-medium">Mbështetje Ekspert</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl font-bold text-purple-400 mb-2 group-hover:scale-110 transition-transform duration-300">5000+</div>
                <div className="text-sm text-white/80 font-medium">Klientë të Kënaqur</div>
              </div>
            </div>
          </div>
          
          {/* Enhanced Floating Card */}
          <div className="relative flex justify-center items-center animate-slide-in-right">
            <div className="relative">
              {/* Floating elements around the card */}
              <div className="absolute -top-6 -left-6 w-12 h-12 rounded-full bg-yellow-400/30 blur-lg animate-pulse"></div>
              <div className="absolute -bottom-6 -right-6 w-16 h-16 rounded-full bg-purple-400/30 blur-lg animate-pulse animation-delay-1000"></div>
              
              <div className="bg-white/10 backdrop-blur-lg border border-white/30 rounded-3xl p-8 animate-float-card hover:scale-105 transition-all duration-500 shadow-2xl"
                   style={{
                     transform: 'perspective(1000px) rotateY(-10deg) rotateX(5deg)',
                     boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)'
                   }}>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-500 flex items-center justify-center mr-3">
                    <span className="text-neutral-900 font-bold">🌟</span>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">
                    E Veçantë Sot
                  </h3>
                </div>
                <p className="text-white/90 leading-relaxed text-lg mb-6">
                  Ushqim organik premium për kafshë me 30% zbritje për klientët e rinj.
                  I jepni kafshëve tuaja ushqyerjen që meritojnë.
                </p>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold text-yellow-400">30% ZBRITJE</div>
                  <Link href="/products" className="px-6 py-2 bg-gradient-to-r from-yellow-400 to-yellow-500 text-neutral-900 font-bold rounded-full hover:shadow-lg transition-all duration-300 hover:scale-105">
                    Merr Ofertën
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ModernHeroSection;
