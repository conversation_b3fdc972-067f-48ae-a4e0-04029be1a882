@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Premium E-commerce Design System - PawLux Inspired with Bright Yellow Theme */
:root {
  /* Primary Brand Colors - Bright Yellow (#ffe003) as Main Brand Color */
  --primary: #ffe003;             /* Main Brand Color - User's preferred bright yellow */
  --primary-light: #fff44f;       /* Lighter shade for hover states and highlights */
  --primary-dark: #e6c700;        /* Darker shade for active states and borders */
  --primary-darker: #ccb300;      /* Even darker shade for text on light backgrounds */
  --primary-gold: #ffb300;        /* Gold accent from design.txt */

  /* Secondary Colors - Professional Complementary Palette from design.txt */
  --secondary-blue: #667eea;      /* Beautiful gradient blue from design.txt */
  --secondary-blue-light: #74b9ff; /* Lighter blue for hover states */
  --secondary-blue-dark: #764ba2;  /* Purple-blue gradient end from design.txt */
  --secondary-dark: #2d3436;      /* Professional dark from design.txt */
  --secondary-gray: #636e72;      /* Medium gray from design.txt */

  /* Accent Colors - Limited and Purposeful */
  --accent-teal: #00a19a;         /* For CTAs and important elements */
  --accent-teal-light: #33b5b0;   /* Lighter teal for hover states */
  --accent-teal-dark: #007a75;    /* Darker teal for active states */

  /* Neutral System - Professional grayscale for better contrast with yellow */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Background System from design.txt */
  --bg-light: #f8f9fa;
  --bg-light-secondary: #e9ecef;

  /* Semantic Colors - Clear and purposeful */
  --success: #00a86b;             /* More professional green */
  --success-light: #33ba88;
  --warning: var(--primary);      /* Using primary yellow as warning */
  --error: #e02020;               /* More professional red */
  --error-light: #e64d4d;
  --info: var(--secondary-blue);

  /* Typography System */
  --font-primary: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Spacing System */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  --space-3xl: 4rem;      /* 64px */

  /* Border Radius */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-2xl: 1.5rem;   /* 24px */
  --radius-3xl: 2rem;     /* 32px */

  /* Premium Gradients from design.txt */
  --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-gold) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-blue-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-teal), var(--accent-teal-light));
  --gradient-hero: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-blue-dark) 100%);
  --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  --gradient-background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-light-secondary) 100%);
  --gradient-dark: linear-gradient(135deg, var(--secondary-dark) 0%, var(--secondary-gray) 100%);

  /* Premium Shadows from design.txt - Enhanced and sophisticated */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-primary: 0 8px 25px rgba(255, 224, 3, 0.3);
  --shadow-primary-lg: 0 15px 35px rgba(255, 224, 3, 0.4);
  --shadow-glow: 0 0 30px rgba(255, 224, 3, 0.3);
  --shadow-card: 0 10px 30px rgba(0, 0, 0, 0.05);
  --shadow-card-hover: 0 25px 50px rgba(0, 0, 0, 0.1);
  --shadow-floating: 0 25px 50px rgba(0, 0, 0, 0.2);
}

/* Modern Base Styles */
@layer base {
  * {
    @apply border-neutral-200;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-neutral-50 text-neutral-800 font-secondary;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Professional Typography Hierarchy */
  h1, h2, h3, h4, h5, h6 {
    @apply font-primary font-bold tracking-tight text-neutral-900;
    line-height: 1.2;
    margin-bottom: 0.5em;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
    font-weight: 800;
    letter-spacing: -0.025em;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
    font-weight: 700;
    letter-spacing: -0.02em;
  }

  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
    font-weight: 600;
    letter-spacing: -0.015em;
  }

  h4 {
    @apply text-xl md:text-2xl;
    font-weight: 600;
  }

  h5 {
    @apply text-lg md:text-xl;
  }

  h6 {
    @apply text-base md:text-lg;
  }

  /* Professional Text Styling */
  p {
    @apply leading-relaxed text-neutral-700;
    margin-bottom: 1em;
    font-size: 1rem;
    line-height: 1.6;
  }

  .text-large {
    font-size: 1.125rem;
    line-height: 1.7;
  }

  .text-small {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  /* Link Styling */
  a {
    @apply transition-colors duration-200 text-primary-600 hover:text-primary-700;
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
  }

  button {
    @apply transition-all duration-200;
  }
}

/* Premium Component Styles - PawLux Inspired */
@layer components {
  /* Premium Button Components with 3D Effects from design.txt */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
    transform: perspective(1000px) rotateX(0deg);
  }

  .btn-primary {
    background: var(--gradient-primary);
    color: var(--secondary-dark);
    border: none;
    box-shadow: var(--shadow-primary);
    border-radius: 50px;
    font-weight: 600;
  }

  .btn-primary:hover {
    transform: perspective(1000px) rotateX(-10deg) translateY(-5px);
    box-shadow: var(--shadow-primary-lg);
  }

  .btn-primary:active {
    transform: perspective(1000px) rotateX(-5deg) translateY(-2px);
  }

  .btn-primary:focus {
    box-shadow: var(--shadow-primary-lg), 0 0 0 3px rgba(255, 224, 3, 0.3);
  }

  .btn-secondary {
    background: var(--gradient-secondary);
    color: white;
    border: none;
    box-shadow: var(--shadow-md);
    border-radius: 50px;
    font-weight: 600;
  }

  .btn-secondary:hover {
    transform: perspective(1000px) rotateX(-10deg) translateY(-5px);
    box-shadow: var(--shadow-lg);
  }

  .btn-outline {
    background: transparent;
    color: var(--primary-darker);
    border: 2px solid var(--primary);
    border-radius: 50px;
  }

  .btn-outline:hover {
    background: var(--primary);
    color: var(--secondary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
  }

  .btn-ghost {
    background: transparent;
    color: var(--neutral-600);
    border: none;
    border-radius: 50px;
  }

  .btn-ghost:hover {
    background: rgba(255, 224, 3, 0.1);
    color: var(--primary-darker);
    transform: translateY(-2px);
  }

  .btn-sm {
    padding: 0.8rem 1.5rem;
    font-size: 0.875rem;
    min-height: 2.5rem;
  }

  .btn-md {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    min-height: 3rem;
  }

  .btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    min-height: 3.5rem;
  }

  .btn-xl {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    min-height: 4rem;
  }

  /* Premium Card Components with 3D Effects from design.txt */
  .card {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-card);
    border: 1px solid var(--neutral-200);
    overflow: hidden;
    position: relative;
    transform: perspective(1000px) rotateX(0deg);
  }

  .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .card-hover {
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    box-shadow: var(--shadow-card-hover);
    transform: perspective(1000px) rotateX(-5deg) translateY(-10px);
    border-color: var(--primary);
  }

  .card-hover:hover::before {
    transform: translateX(0);
  }

  .card-featured {
    border: 2px solid var(--primary);
    box-shadow: var(--shadow-primary);
    background: linear-gradient(to bottom right, white, rgba(255, 224, 3, 0.05));
  }

  .card-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
  }

  .card-body {
    padding: 1.5rem;
  }

  .card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--neutral-200);
    background: linear-gradient(to right, var(--neutral-50), rgba(255, 224, 3, 0.05));
  }

  .card-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid var(--neutral-200);
    background: linear-gradient(to right, var(--neutral-50), rgba(255, 224, 3, 0.05));
  }

  /* Mobile-optimized cards */
  @media (max-width: 640px) {
    .card-body {
      padding: 1rem;
    }

    .card-header,
    .card-footer {
      padding: 0.75rem 1rem;
    }
  }

  /* Glass Effect */
  .glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Professional Gradient Backgrounds */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  .gradient-hero {
    background: var(--gradient-hero);
  }

  /* Text Gradients */
  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Special Effects */
  .glow-primary {
    box-shadow: var(--shadow-glow);
  }

  .border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box, var(--gradient-primary) border-box;
  }
}

/* Premium Animations from design.txt */
@layer utilities {
  /* Sophisticated Animations */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes scaleIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Premium floating animation from design.txt */
  @keyframes floatCard {
    0%, 100% { transform: perspective(1000px) rotateY(-15deg) rotateX(10deg) translateY(0px); }
    50% { transform: perspective(1000px) rotateY(-15deg) rotateX(10deg) translateY(-20px); }
  }

  /* Background pattern animation from design.txt */
  @keyframes floatPattern {
    0% { transform: translateX(0); }
    100% { transform: translateX(-100px); }
  }

  @keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Pulse animation from design.txt */
  @keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.3; }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(255, 224, 3, 0.5); }
    50% { box-shadow: 0 0 20px rgba(255, 224, 3, 0.8); }
  }

  @keyframes rotate3d {
    0% { transform: rotateY(0deg) rotateX(0deg); }
    100% { transform: rotateY(360deg) rotateX(360deg); }
  }

  @keyframes float3d {
    0%, 100% { transform: translateY(0px) rotateY(0deg); }
    50% { transform: translateY(-20px) rotateY(10deg); }
  }

  /* Premium Animation Classes */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-float-card {
    animation: floatCard 6s ease-in-out infinite;
  }

  .animate-float-pattern {
    animation: floatPattern 20s infinite linear;
  }

  .animate-pulse {
    animation: pulse 3s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-rotate {
    animation: rotate 30s linear infinite;
  }

  .animate-rotate-3d {
    animation: rotate3d 20s linear infinite;
  }

  .animate-float-3d {
    animation: float3d 4s ease-in-out infinite;
  }

  /* Animation Delays */
  .animation-delay-200 {
    animation-delay: 0.2s;
  }

  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  .animation-delay-600 {
    animation-delay: 0.6s;
  }

  .animation-delay-1000 {
    animation-delay: 1s;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-3000 {
    animation-delay: 3s;
  }

  /* Text Gradients */
  .text-gradient-primary {
    background: linear-gradient(135deg, var(--primary-emerald), var(--primary-emerald-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-secondary {
    background: linear-gradient(135deg, var(--primary-orange), var(--primary-orange-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 3D Transform Utilities */
  .transform-3d {
    transform-style: preserve-3d;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .perspective-2000 {
    perspective: 2000px;
  }

  .rotate-y-12 {
    transform: rotateY(12deg);
  }

  .rotate-y-n12 {
    transform: rotateY(-12deg);
  }

  .rotate-x-12 {
    transform: rotateX(12deg);
  }

  .rotate-x-n12 {
    transform: rotateX(-12deg);
  }

  /* Enhanced Utility Classes */
  .container-custom {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .container-custom {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding: 0 2rem;
    }
  }

  .section-padding {
    padding: 3rem 0;
  }

  @media (min-width: 640px) {
    .section-padding {
      padding: 4rem 0;
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding: 5rem 0;
    }
  }

  .section-padding-sm {
    padding: 2rem 0;
  }

  @media (min-width: 640px) {
    .section-padding-sm {
      padding: 2.5rem 0;
    }
  }

  @media (min-width: 1024px) {
    .section-padding-sm {
      padding: 3rem 0;
    }
  }

  /* Mobile-first responsive utilities */
  .mobile-padding {
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .mobile-padding {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .mobile-padding {
      padding: 0 2rem;
    }
  }

  .mobile-text-center {
    text-align: center;
  }

  @media (min-width: 640px) {
    .mobile-text-center {
      text-align: left;
    }
  }

  .mobile-stack {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-stack {
      flex-direction: row;
      gap: 1.5rem;
    }
  }
}
