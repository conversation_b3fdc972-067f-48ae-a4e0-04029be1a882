'use client';

import React from 'react';
import Link from 'next/link';

const ModernCategoryShowcase: React.FC = () => {
  const categories = [
    {
      id: 1,
      name: 'Qen',
      icon: '🐕',
      productCount: '500+ produkte',
      gradient: 'from-blue-500 to-blue-700',
      description: 'Ushqim, lodra dhe aksesorë për qenin tuaj',
      href: '/products/qen'
    },
    {
      id: 2,
      name: 'Mace',
      icon: '🐱',
      productCount: '450+ produkte',
      gradient: 'from-pink-500 to-pink-700',
      description: 'Gjithçka që i duhet maces suaj',
      href: '/products/mace'
    },
    {
      id: 3,
      name: 'Zogj',
      icon: '🐦',
      productCount: '200+ produkte',
      gradient: 'from-yellow-500 to-orange-600',
      description: 'Kafaze, ushqim dhe aksesorë për zogjtë',
      href: '/products/zogj'
    },
    {
      id: 4,
      name: '<PERSON><PERSON><PERSON>',
      icon: '🐠',
      productCount: '300+ produkte',
      gradient: 'from-cyan-500 to-blue-600',
      description: 'Akuariumë dhe ushqim për peshqit',
      href: '/products/peshq'
    },
    {
      id: 5,
      name: 'Kafshë të Vogla',
      icon: '🐹',
      productCount: '150+ produkte',
      gradient: 'from-green-500 to-emerald-700',
      description: 'Për lepuj, hamsterë dhe kafshë të tjera',
      href: '/products/kafshë-të-vogla'
    }
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-neutral-50 to-neutral-100">
      <div className="container-custom">
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 rounded-full text-sm font-bold mb-8 border-2 border-primary-300 shadow-md hover:shadow-lg transition-all duration-300">
            <span className="mr-2 animate-bounce">🐾</span>
            Eksploro sipas Kafshës
          </div>
          <h2 className="text-4xl lg:text-5xl font-primary font-bold mb-8">
            Gjej Produktet e <span className="text-primary-600 animate-pulse">Duhura</span> për <span className="text-blue-600">Mikun Tënd</span>
          </h2>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
            Çdo kafshë ka nevojat e veta të veçanta. Eksploro koleksionin tonë të kuruar me kujdes
            për të gjetur produktet më të mira, më të sigurta dhe më cilësore për mikun tënd të dashur.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 sm:gap-8">
          {categories.map((category, index) => (
            <Link
              key={category.id}
              href={category.href}
              className="group block animate-fade-in hover:scale-105 transition-all duration-300"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="card card-hover h-full bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-neutral-200 hover:border-primary-300">
                <div className="card-body text-center p-6">
                  {/* Enhanced Icon with gradient background and animation */}
                  <div className={`w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-6 rounded-2xl bg-gradient-to-br ${category.gradient} flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-lg group-hover:shadow-xl transform group-hover:-translate-y-2`}>
                    <span className="text-4xl sm:text-5xl animate-pulse">{category.icon}</span>
                  </div>

                  <h3 className="text-lg sm:text-xl font-primary font-bold text-neutral-800 mb-3 group-hover:text-primary-600 transition-all duration-300">
                    {category.name}
                  </h3>

                  <div className="inline-block px-4 py-2 bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 rounded-full text-sm font-bold mb-4 border border-primary-300 shadow-sm group-hover:shadow-md transition-all duration-300">
                    {category.productCount}
                  </div>

                  <p className="text-neutral-600 text-sm leading-relaxed mb-6 group-hover:text-neutral-700 transition-colors duration-300">
                    {category.description}
                  </p>

                  <div className="flex items-center justify-center text-primary-600 font-bold group-hover:text-primary-700 transition-all duration-300 bg-gradient-to-r from-primary-50 to-primary-100 py-3 px-4 rounded-lg group-hover:from-primary-100 group-hover:to-primary-200 shadow-sm group-hover:shadow-md">
                    <span className="text-sm">Shiko Produktet</span>
                    <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ModernCategoryShowcase;
