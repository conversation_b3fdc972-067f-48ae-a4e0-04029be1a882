'use client';

import React from 'react';
import Link from 'next/link';
import ProductCard from '@/components/molecules/ProductCard';

interface Product {
  id: string;
  name: string;
  price: number;
  oldPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  isNew?: boolean;
  discount?: number;
  badge?: string;
  isOnSale?: boolean;
}

const FeaturedProducts: React.FC = () => {
  const products: Product[] = [
    {
      id: '1',
      name: 'Ushqim Premium për Qen - Royal Canin 15kg',
      price: 2200,
      oldPrice: 2500,
      image: '/images/placeholder.svg',
      rating: 4.8,
      reviewCount: 156,
      category: 'Ushqim për Qen',
      badge: 'Bestseller',
      isOnSale: true,
      discount: 12
    },
    {
      id: '2',
      name: 'Lodër Interaktive për Mace - SmartToy',
      price: 850,
      image: '/images/placeholder.svg',
      rating: 4.6,
      reviewCount: 89,
      category: 'Lodra për Mace',
      isNew: true
    },
    {
      id: '3',
      name: 'Akuarium Modern 100L me LED',
      price: 12000,
      oldPrice: 14000,
      image: '/images/placeholder.svg',
      rating: 4.9,
      reviewCount: 45,
      category: 'Akuarium',
      isOnSale: true,
      discount: 14
    },
    {
      id: '4',
      name: 'Kafaz Luksoz për Zogj - Villa Bird',
      price: 4500,
      image: '/images/placeholder.svg',
      rating: 4.7,
      reviewCount: 67,
      category: 'Kafaze për Zogj',
      badge: 'Premium'
    },
    {
      id: '5',
      name: 'Set Aksesorësh për Qen - Complete Care',
      price: 1800,
      oldPrice: 2200,
      image: '/images/placeholder.svg',
      rating: 4.5,
      reviewCount: 123,
      category: 'Aksesorë për Qen',
      isOnSale: true,
      discount: 18
    },
    {
      id: '6',
      name: 'Shtrat Ortopedik për Mace - ComfortMax',
      price: 1500,
      image: '/images/placeholder.svg',
      rating: 4.8,
      reviewCount: 78,
      category: 'Shtretër për Mace',
      isNew: true
    }
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-white to-neutral-50">
      <div className="container-custom">
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-100 to-orange-100 text-red-700 rounded-full text-sm font-bold mb-8 border-2 border-red-200 shadow-md hover:shadow-lg transition-all duration-300">
            <span className="mr-2 animate-pulse">🔥</span>
            Produktet më të Populluara
          </div>
          <h2 className="text-4xl lg:text-5xl font-primary font-bold mb-8">
            Produktet e <span className="text-primary-600 animate-pulse">Rekomanduara</span>
          </h2>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
            Zbulo produktet më të shitura dhe më të vlerësuara nga komuniteti ynë i dashuruesve të kafshëve.
            Çdo produkt është testuar dhe aprovuar nga ekspertët tanë veterinarë.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12">
          {products.map((product, index) => (
            <div
              key={product.id}
              className="animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <ProductCard product={product} />
            </div>
          ))}
        </div>

        {/* Enhanced View All Products Button */}
        <div className="text-center animate-fade-in animation-delay-400">
          <Link href="/products" className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 text-neutral-900 font-bold rounded-full shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-500 hover:from-primary-600 hover:to-primary-700">
            Shiko të Gjitha Produktet
            <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
