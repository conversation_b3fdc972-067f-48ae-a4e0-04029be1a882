'use client';

import React, { useState } from 'react';

interface Product {
  id: string;
  name: string;
  price: number;
  oldPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  isNew?: boolean;
  discount?: number;
}

interface ProductCardProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [isLiked, setIsLiked] = useState(false);

  return (
    <div className="card card-hover group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-neutral-200 hover:border-primary-300 overflow-hidden">
      {/* Product Badge */}
      {product.isNew && (
        <div className="absolute top-3 left-3 bg-gradient-to-r from-primary-400 to-primary-500 text-neutral-900 text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md animate-pulse">
          🆕 E Re
        </div>
      )}

      {product.discount && (
        <div className="absolute top-3 right-3 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md animate-pulse">
          -{product.discount}%
        </div>
      )}
      
      {/* Enhanced Product Image with Modern Animation */}
      <div className="relative overflow-hidden">
        <div className="aspect-square flex items-center justify-center group-hover:scale-110 transition-all duration-700 relative bg-gradient-to-br from-neutral-100 to-neutral-200">
          {/* Floating background elements */}
          <div className="absolute top-1/4 left-1/4 w-16 h-16 rounded-full bg-primary-400/10 blur-xl animate-float"></div>
          <div className="absolute bottom-1/3 right-1/3 w-20 h-20 rounded-full bg-blue-400/10 blur-xl animate-float animation-delay-2000"></div>

          {/* Product icon with enhanced animation - Dynamic based on category */}
          <div className="text-6xl opacity-80 group-hover:opacity-100 transition-all duration-500 relative z-10 transform group-hover:scale-110 group-hover:rotate-6">
            {product.category.includes('Qen') ? '🐕' :
             product.category.includes('Mace') ? '🐱' :
             product.category.includes('Zogj') ? '🐦' :
             product.category.includes('Peshq') || product.category.includes('Akuarium') ? '🐠' :
             product.category.includes('Lodra') ? '🎾' :
             product.category.includes('Ushqim') ? '🥘' :
             '📦'}
          </div>

          {/* Product type overlay */}
          <div className="absolute bottom-2 left-2 bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 text-xs font-medium text-neutral-700 opacity-0 group-hover:opacity-100 transition-all duration-300">
            {product.category.includes('Ushqim') ? 'Ushqim' :
             product.category.includes('Lodra') ? 'Lodër' :
             product.category.includes('Akuarium') ? 'Akuarium' :
             product.category.includes('Aksesorë') ? 'Aksesor' :
             'Produkt'}
          </div>
        </div>

        {/* Enhanced Quick Actions */}
        <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-4 group-hover:translate-x-0">
          <button
            className={`w-10 h-10 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 ${
              isLiked 
                ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-red-200' 
                : 'bg-white text-neutral-600 hover:bg-red-50 hover:text-red-500 shadow-neutral-200'
            }`}
            onClick={() => setIsLiked(!isLiked)}
          >
            <svg className="w-5 h-5" fill={isLiked ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </button>
          <button className="w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-blue-50 hover:scale-110 transition-all duration-300 text-neutral-600 hover:text-blue-600 shadow-neutral-200">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
          <button className="w-10 h-10 bg-gradient-to-r from-primary-400 to-primary-500 rounded-full shadow-lg flex items-center justify-center hover:from-primary-500 hover:to-primary-600 hover:scale-110 transition-all duration-300 text-neutral-900 shadow-primary-200">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Enhanced Product Info */}
      <div className="card-body p-5">
        <div className="text-xs text-neutral-500 mb-2 font-medium uppercase tracking-wide group-hover:text-primary-600 transition-colors duration-300">
          {product.category}
        </div>
        <h3 className="font-semibold text-neutral-800 mb-3 group-hover:text-primary-600 transition-colors duration-300 line-clamp-2 text-lg">
          {product.name}
        </h3>

        {/* Enhanced Rating */}
        <div className="flex items-center gap-2 mb-4">
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              <span
                key={i}
                className={`text-sm transition-all duration-300 ${
                  i < Math.floor(product.rating) 
                    ? 'text-yellow-400 transform scale-110' 
                    : 'text-neutral-300'
                }`}
              >
                ★
              </span>
            ))}
          </div>
          <span className="text-xs text-neutral-500 font-medium group-hover:text-neutral-700 transition-colors duration-300">
            ({product.reviewCount})
          </span>
        </div>

        {/* Enhanced Price */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-xl font-bold text-neutral-800 group-hover:text-primary-600 transition-colors duration-300">
                {product.price} Lekë
              </span>
              {product.oldPrice && (
                <span className="text-sm text-neutral-500 line-through group-hover:text-neutral-600 transition-colors duration-300">
                  {product.oldPrice} Lekë
                </span>
              )}
            </div>
            {product.oldPrice && (
              <span className="text-xs text-green-600 font-medium mt-1 animate-pulse">
                Kursim: {product.oldPrice - product.price} Lekë
              </span>
            )}
          </div>
          {product.oldPrice && (
            <span className="text-sm font-bold text-red-600 bg-red-50 px-2 py-1 rounded-full animate-pulse">
              -{Math.round(((product.oldPrice - product.price) / product.oldPrice) * 100)}%
            </span>
          )}
        </div>

        {/* Enhanced Add to Cart Button */}
        <button className="w-full py-3 px-4 rounded-lg font-semibold transition-all duration-500 flex items-center justify-center group/btn bg-gradient-to-r from-neutral-800 to-neutral-900 text-white hover:from-primary-400 hover:to-primary-500 hover:text-neutral-900 transform hover:-translate-y-1 hover:shadow-lg">
          <svg className="w-4 h-4 mr-2 group-hover/btn:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
          </svg>
          Shto në Shportë
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
