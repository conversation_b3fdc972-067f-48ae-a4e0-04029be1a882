'use client';

import React from 'react';

const PremiumFeaturesSection: React.FC = () => {
  const features = [
    {
      icon: '🚚',
      title: 'Transport Falas',
      description: 'Dërgesa falas për porosi mbi 5,000 Lekë. Transport i shpejtë dhe i besueshëm në derën tuaj brenda 2-3 ditëve.',
      color: 'primary'
    },
    {
      icon: '🏆',
      title: 'Cilësi Premium',
      description: 'Të gjitha produktet janë zgjedhur dhe testuar me kujdes për të siguruar standardet më të larta të cilësisë për kafshët tuaja.',
      color: 'blue'
    },
    {
      icon: '💝',
      title: 'Garanci Kënaqësie',
      description: 'Garanci 30-ditore kthimi parash. Nëse nuk jeni të kënaqur, ne do ta rregullojmë ose do t\'i kthejmë paratë.',
      color: 'teal'
    },
    {
      icon: '🩺',
      title: '<PERSON>tuar nga Veterinerët',
      description: 'Produktet tona janë rekomanduar nga veterinerë dhe profesionistë të kujdesit për kafshë në të gjithë vendin.',
      color: 'green'
    }
  ];

  return (
    <section className="relative overflow-hidden py-20"
             style={{
               background: 'linear-gradient(135deg, #2d3436 0%, #636e72 100%)'
             }}>
      {/* Animated Background Pattern from design.txt */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 animate-rotate"
             style={{
               backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200'%3E%3Cpolygon points='100,10 40,180 190,60 10,60 160,180' fill='rgba(255,224,3,0.03)'/%3E%3C/svg%3E")`,
               backgroundRepeat: 'repeat'
             }} />
      </div>

      <div className="container-custom relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-primary font-bold text-white mb-8">
            Pse të Zgjedhësh{' '}
            <span style={{
              background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              PetShop AL
            </span>
            ?
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group relative"
            >
              {/* Premium Glass Card with 3D Effects from design.txt */}
              <div className="bg-white/10 backdrop-blur-md border border-white/10 rounded-3xl p-8 text-center transition-all duration-300 h-full"
                   style={{
                     transform: 'perspective(1000px) rotateX(0deg)'
                   }}
                   onMouseEnter={(e) => {
                     e.currentTarget.style.transform = 'perspective(1000px) rotateX(-10deg) translateY(-10px)';
                     e.currentTarget.style.background = 'rgba(255, 224, 3, 0.1)';
                     e.currentTarget.style.borderColor = 'rgba(255, 224, 3, 0.3)';
                   }}
                   onMouseLeave={(e) => {
                     e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) translateY(0px)';
                     e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                     e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                   }}>
                
                {/* Premium Icon with Gradient Background */}
                <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl transition-all duration-300 group-hover:scale-110"
                     style={{
                       background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                       boxShadow: '0 10px 30px rgba(255, 224, 3, 0.3)'
                     }}>
                  {feature.icon}
                </div>

                <h3 className="text-xl font-primary font-bold text-white mb-4 group-hover:text-primary-300 transition-colors">
                  {feature.title}
                </h3>

                <p className="text-white/80 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PremiumFeaturesSection;
