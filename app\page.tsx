'use client';

import ModernHeader from '@/components/organisms/ModernHeader';
import ModernHeroSection from '@/components/organisms/ModernHeroSection';
import ModernCategoryShowcase from '@/components/organisms/ModernCategoryShowcase';
import FeaturedProducts from '@/components/organisms/FeaturedProducts';
import Footer from '@/components/organisms/Footer';
import TrustBadges from '@/components/molecules/TrustBadges';
import QuickActions from '@/components/molecules/QuickActions';
import PremiumFeaturesSection from '@/components/organisms/PremiumFeaturesSection';
import PremiumInteractions from '@/components/utils/PremiumInteractions';
import { useEffect } from 'react';
import { registerServiceWorker } from './register-sw';

export default function Home() {
  useEffect(() => {
    registerServiceWorker();
  }, []);

  return (
    <div className="min-h-screen bg-neutral-50">
      <PremiumInteractions />
      <ModernHeader />
      <main>
        <ModernHeroSection />
        <TrustBadges />
        <ModernCategoryShowcase />
        <FeaturedProducts />
        <PremiumFeaturesSection />

        {/* Professional Benefits Section */}
        <section className="section-padding bg-neutral-50">
          <div className="container-custom">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-6 py-3 bg-primary-100 text-primary-700 rounded-full text-sm font-bold mb-8 border-2 border-primary-200">
                ✨ Përfitimet Tona
              </div>
              <h2 className="text-4xl lg:text-5xl font-primary font-bold mb-8">
                Pse të Zgjedhësh <span className="text-primary-600">PetShop Albania</span>?
              </h2>
              <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
                Jemi më shumë se një dyqan - jemi partnerët tuaj të besueshëm në kujdesin për kafshët e dashura.
                Çdo ditë punojmë për të sjellë cilësinë më të mirë dhe shërbimin më të shkëlqyer.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Professional Benefit 1 */}
              <div className="card card-hover text-center group">
                <div className="card-body">
                  <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary-400 to-primary-600 rounded-2xl flex items-center justify-center group-hover:scale-105 transition-all duration-300 shadow-lg">
                    <span className="text-4xl">🚚</span>
                  </div>
                  <h3 className="text-xl font-primary font-bold text-neutral-800 mb-4 group-hover:text-primary-600 transition-colors">
                    Transport Falas
                  </h3>
                  <p className="text-neutral-600 leading-relaxed mb-4">
                    Transport falas për porosi mbi 5000 Lekë në të gjithë Shqipërinë.
                    Dërgesa të shpejta dhe të sigurta me tracking në kohë reale.
                  </p>
                  <div className="bg-primary-50 text-primary-700 font-bold py-2 px-4 rounded-lg border border-primary-200">
                    Dërgesa brenda 24-48 orësh
                  </div>
                </div>
              </div>

              {/* Professional Benefit 2 */}
              <div className="card card-hover text-center group">
                <div className="card-body">
                  <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-105 transition-all duration-300 shadow-lg">
                    <span className="text-4xl">🏥</span>
                  </div>
                  <h3 className="text-xl font-primary font-bold text-neutral-800 mb-4 group-hover:text-blue-600 transition-colors">
                    Konsulencë Veterinare
                  </h3>
                  <p className="text-neutral-600 leading-relaxed mb-4">
                    Ekip veterinarësh të kualifikuar për t'ju ndihmuar në zgjedhjen
                    e produkteve të duhura për kafshën tuaj dhe këshilla profesionale.
                  </p>
                  <div className="bg-blue-50 text-blue-700 font-bold py-2 px-4 rounded-lg border border-blue-200">
                    Konsulencë falas 24/7
                  </div>
                </div>
              </div>

              {/* Professional Benefit 3 */}
              <div className="card card-hover text-center group">
                <div className="card-body">
                  <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-teal-400 to-teal-600 rounded-2xl flex items-center justify-center group-hover:scale-105 transition-all duration-300 shadow-lg">
                    <span className="text-4xl">🎁</span>
                  </div>
                  <h3 className="text-xl font-primary font-bold text-neutral-800 mb-4 group-hover:text-teal-600 transition-colors">
                    Program Besnikërie
                  </h3>
                  <p className="text-neutral-600 leading-relaxed mb-4">
                    Fito pikë me çdo blerje dhe shkëmbei me produkte, zbritje dhe
                    ofertat ekskluzive. Sa më shumë blini, aq më shumë kurseni.
                  </p>
                  <div className="bg-teal-50 text-teal-700 font-bold py-2 px-4 rounded-lg border border-teal-200">
                    Deri në 20% zbritje
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Professional Testimonials */}
        <section className="section-padding bg-gradient-to-br from-neutral-50 to-neutral-100">
          <div className="container-custom">
            <div className="text-center mb-16 animate-fade-in">
              <div className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 rounded-full text-sm font-bold mb-8 border-2 border-primary-300 shadow-md hover:shadow-lg transition-all duration-300">
                <span className="mr-2 animate-bounce">💬</span>
                Testimonialet
              </div>
              <h2 className="text-4xl lg:text-6xl font-primary font-bold mb-8">
                Çfarë Thonë <span className="text-primary-600 animate-pulse">Klientët Tanë</span>
              </h2>
              <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
                Mijëra pronarë kafshësh kanë zgjedhur PetShop Albania për nevojat e tyre dhe
                janë të kënaqur me cilësinë dhe shërbimin tonë.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  name: "Elira Hoxha",
                  location: "Tiranë",
                  avatar: "E",
                  rating: 5,
                  text: "Shërbimi më i mirë për kafshët! Produktet janë të cilësisë së lartë dhe transporti shumë i shpejtë. Qeni im adhuron ushqimin e ri që bleva këtu."
                },
                {
                  name: "Arben Krasniqi",
                  location: "Durrës",
                  avatar: "A",
                  rating: 5,
                  text: "Çmimet janë konkurruese dhe varieteti i produkteve është i mahnitshëm. Stafi është shumë i ndihmës për të zgjedhur produktet e duhura."
                },
                {
                  name: "Manjola Duka",
                  location: "Vlorë",
                  avatar: "M",
                  rating: 5,
                  text: "Kam blerë akuarium dhe të gjitha aksesorët këtu. Cilësia është e shkëlqyer dhe konsulenca veterinare më ndihmoi shumë."
                }
              ].map((testimonial, index) => (
                <div
                  key={index}
                  className="group relative animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Premium Glass Card with 3D Effects */}
                  <div className="bg-white/80 backdrop-blur-lg border border-white/50 rounded-3xl p-8 transition-all duration-500 h-full shadow-lg hover:shadow-2xl"
                       style={{
                         transform: 'perspective(1000px) rotateX(0deg)'
                       }}
                       onMouseEnter={(e) => {
                         e.currentTarget.style.transform = 'perspective(1000px) rotateX(-5deg) translateY(-10px)';
                         e.currentTarget.style.background = 'rgba(255, 224, 3, 0.1)';
                         e.currentTarget.style.borderColor = 'rgba(255, 224, 3, 0.3)';
                       }}
                       onMouseLeave={(e) => {
                         e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) translateY(0px)';
                         e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                         e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.5)';
                       }}>

                    {/* Quote Icon */}
                    <div className="absolute top-4 right-4 text-primary-300 text-4xl opacity-50">
                      "
                    </div>

                    <div className="flex items-center mb-6">
                      <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-neutral-900 font-bold text-2xl mr-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                        {testimonial.avatar}
                      </div>
                      <div>
                        <h4 className="font-primary font-bold text-neutral-800 text-xl group-hover:text-primary-600 transition-colors">{testimonial.name}</h4>
                        <p className="text-sm text-neutral-500 font-medium mb-2">{testimonial.location}</p>
                        <div className="flex text-primary-500 text-xl">
                          {Array.from({ length: testimonial.rating }, (_, i) => (
                            <span key={i} className="animate-pulse" style={{ animationDelay: `${i * 0.1}s` }}>★</span>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="relative">
                      <p className="text-neutral-700 leading-relaxed italic font-medium text-lg">
                        "{testimonial.text}"
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Professional Newsletter Section */}
        <section className="relative overflow-hidden py-20"
                 style={{
                   background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 50%, #667eea 100%)'
                 }}>
          {/* Animated Background Pattern */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 animate-float-pattern opacity-20"
                 style={{
                   backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200'%3E%3Cpolygon points='100,10 40,180 190,60 10,60 160,180' fill='rgba(255,255,255,0.1)'/%3E%3C/svg%3E")`,
                   backgroundRepeat: 'repeat'
                 }} />
          </div>

          {/* Floating Elements */}
          <div className="absolute top-1/4 left-1/4 w-16 h-16 rounded-full bg-white/20 blur-xl animate-float"></div>
          <div className="absolute bottom-1/3 right-1/4 w-24 h-24 rounded-full bg-white/20 blur-xl animate-float animation-delay-2000"></div>
          <div className="absolute top-1/2 right-1/3 w-20 h-20 rounded-full bg-white/20 blur-xl animate-float animation-delay-3000"></div>

          <div className="container-custom relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center px-8 py-4 bg-primary-400/20 backdrop-blur-md border-2 border-primary-400/30 text-primary-400 rounded-full text-sm font-bold mb-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <span className="mr-2 animate-pulse">📧</span>
                Newsletter Ekskluziv
              </div>

              <h2 className="text-4xl lg:text-6xl font-primary font-bold mb-8 text-white">
                Mos Humb Asnjë <span className="text-primary-400 drop-shadow-lg">Ofertë</span>
              </h2>

              <p className="text-xl mb-12 leading-relaxed max-w-3xl mx-auto font-medium text-neutral-200">
                Abonohu në newsletter-in tonë dhe merr ofertat ekskluzive, këshilla profesionale për kujdesin
                e kafshëve dhe lajmet e fundit nga bota e produkteve për kafshë.
              </p>

              {/* Enhanced Newsletter Form */}
              <div className="max-w-2xl mx-auto mb-12">
                <div className="bg-white/20 backdrop-blur-lg border border-white/30 rounded-3xl p-8 shadow-2xl">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex-1 relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <input
                        type="email"
                        placeholder="Shkruaj email-in tënd këtu..."
                        className="w-full pl-12 pr-4 py-4 rounded-2xl text-neutral-800 placeholder-neutral-500 focus:outline-none focus:ring-4 focus:ring-white/50 border-2 border-white/30 bg-white/90 backdrop-blur-sm font-medium text-lg"
                      />
                    </div>
                    <button className="bg-gradient-to-r from-neutral-800 to-neutral-900 hover:from-neutral-900 hover:to-black text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl whitespace-nowrap">
                      <svg className="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      Abonohu Tani
                    </button>
                  </div>
                </div>
              </div>

              {/* Enhanced Benefits Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-6 text-center transition-all duration-300 hover:scale-105 hover:bg-white/30">
                  <div className="text-3xl mb-3">🎁</div>
                  <div className="text-lg font-bold text-neutral-900 mb-2">10% Zbritje</div>
                  <div className="text-sm text-neutral-700">për anëtarët e rinj</div>
                </div>
                <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-6 text-center transition-all duration-300 hover:scale-105 hover:bg-white/30">
                  <div className="text-3xl mb-3">📱</div>
                  <div className="text-lg font-bold text-neutral-900 mb-2">Lajme Ekskluzive</div>
                  <div className="text-sm text-neutral-700">dhe oferta speciale</div>
                </div>
                <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-6 text-center transition-all duration-300 hover:scale-105 hover:bg-white/30">
                  <div className="text-3xl mb-3">🏆</div>
                  <div className="text-lg font-bold text-neutral-900 mb-2">Këshilla Ekspert</div>
                  <div className="text-sm text-neutral-700">nga veterinerët</div>
                </div>
              </div>

              <p className="text-sm text-neutral-700 font-medium bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 inline-block">
                * Mund të çabonohesh në çdo kohë. Ne respektojmë privatësinë tënde.
              </p>
            </div>
          </div>
        </section>
      </main>

      <Footer />

      {/* Quick Actions Floating Button */}
      <QuickActions />
    </div>
  );
}
