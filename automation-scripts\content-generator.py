#!/usr/bin/env python3
"""
SEO Content Generator & Optimizer
Generates SEO-optimized content templates and meta tags
"""

import json
import csv
import re
from datetime import datetime
import random

class ContentGenerator:
    def __init__(self):
        self.templates = {
            'product_descriptions': [
                "Discover our premium {product} designed for {target_audience}. {key_benefit} with {feature_1} and {feature_2}. Perfect for {use_case}.",
                "Experience the ultimate {product} that delivers {key_benefit}. Featuring {feature_1} and {feature_2}, it's ideal for {target_audience} who want {use_case}.",
                "Transform your {category} experience with our {product}. {key_benefit} through advanced {feature_1} and innovative {feature_2}."
            ],
            'blog_intros': [
                "Are you looking for the best {topic}? In this comprehensive guide, we'll explore {key_points} and help you {goal}.",
                "When it comes to {topic}, there are many factors to consider. This article covers {key_points} to help you {goal}.",
                "{topic} can be challenging, but with the right approach, you can {goal}. Let's dive into {key_points}."
            ],
            'meta_descriptions': [
                "Discover {main_keyword} solutions that {benefit}. Learn about {feature_1}, {feature_2}, and more. {cta}",
                "Find the best {main_keyword} for {target_audience}. Compare {feature_1} vs {feature_2}. {cta}",
                "Expert guide to {main_keyword}. Everything you need to know about {feature_1} and {feature_2}. {cta}"
            ]
        }
        
        self.cta_phrases = [
            "Get started today!",
            "Learn more now!",
            "Shop our collection!",
            "Read the full guide!",
            "Compare options now!",
            "Find your perfect match!"
        ]
    
    def generate_product_content(self, product_data):
        """Generate SEO-optimized product content"""
        content = {}
        
        # Product title optimization
        title_templates = [
            "{brand} {product_name} - {key_benefit} | {category}",
            "{product_name} by {brand} - Premium {category} with {key_feature}",
            "Best {category}: {brand} {product_name} - {key_benefit}"
        ]
        
        content['title'] = random.choice(title_templates).format(**product_data)
        
        # Meta description
        meta_template = random.choice(self.templates['meta_descriptions'])
        content['meta_description'] = meta_template.format(
            main_keyword=product_data.get('main_keyword', product_data['category']),
            target_audience=product_data.get('target_audience', 'customers'),
            feature_1=product_data.get('feature_1', 'quality'),
            feature_2=product_data.get('feature_2', 'durability'),
            benefit=product_data.get('key_benefit', 'excellent results'),
            cta=random.choice(self.cta_phrases)
        )
        
        # Product description
        desc_template = random.choice(self.templates['product_descriptions'])
        content['description'] = desc_template.format(**product_data)
        
        # H1 tag
        content['h1'] = f"{product_data['brand']} {product_data['product_name']}"
        
        # H2 tags suggestions
        content['h2_suggestions'] = [
            f"Key Features of {product_data['product_name']}",
            f"Benefits of {product_data['category']}",
            f"Why Choose {product_data['brand']}?",
            "Customer Reviews and Ratings",
            "Specifications and Details",
            "Shipping and Returns"
        ]
        
        # Schema markup
        content['schema_product'] = {
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": content['h1'],
            "description": content['description'],
            "brand": {
                "@type": "Brand",
                "name": product_data['brand']
            },
            "category": product_data['category']
        }
        
        return content
    
    def generate_blog_content(self, topic_data):
        """Generate SEO blog content structure"""
        content = {}
        
        # Blog title optimization
        title_templates = [
            "The Ultimate Guide to {main_topic} in {year}",
            "{number} Best {main_topic} Tips for {target_audience}",
            "How to {action} with {main_topic}: Complete Guide",
            "{main_topic} vs {alternative}: Which is Better?",
            "Everything You Need to Know About {main_topic}"
        ]
        
        content['title'] = random.choice(title_templates).format(
            year=datetime.now().year,
            number=random.choice([5, 7, 10, 15, 20]),
            **topic_data
        )
        
        # Meta description
        content['meta_description'] = random.choice(self.templates['meta_descriptions']).format(
            main_keyword=topic_data['main_topic'],
            target_audience=topic_data.get('target_audience', 'readers'),
            feature_1=topic_data.get('subtopic_1', 'tips'),
            feature_2=topic_data.get('subtopic_2', 'strategies'),
            benefit=topic_data.get('benefit', 'better results'),
            cta=random.choice(self.cta_phrases)
        )
        
        # Introduction
        intro_template = random.choice(self.templates['blog_intros'])
        content['introduction'] = intro_template.format(
            topic=topic_data['main_topic'],
            key_points=f"{topic_data.get('subtopic_1', 'key concepts')}, {topic_data.get('subtopic_2', 'best practices')}, and {topic_data.get('subtopic_3', 'expert tips')}",
            goal=topic_data.get('goal', 'achieve your objectives')
        )
        
        # Content outline
        content['outline'] = [
            f"What is {topic_data['main_topic']}?",
            f"Benefits of {topic_data['main_topic']}",
            f"How to Get Started with {topic_data['main_topic']}",
            f"Best Practices for {topic_data['main_topic']}",
            f"Common Mistakes to Avoid",
            f"Tools and Resources",
            "Conclusion and Next Steps"
        ]
        
        # FAQ suggestions
        content['faq_suggestions'] = [
            f"What is the best way to {topic_data.get('action', 'start')} with {topic_data['main_topic']}?",
            f"How long does it take to see results from {topic_data['main_topic']}?",
            f"What are the costs associated with {topic_data['main_topic']}?",
            f"Can beginners succeed with {topic_data['main_topic']}?",
            f"What tools do I need for {topic_data['main_topic']}?"
        ]
        
        return content
    
    def optimize_existing_content(self, text, target_keyword, keyword_density=2.5):
        """Optimize existing content for SEO"""
        words = text.split()
        total_words = len(words)
        target_occurrences = max(1, int(total_words * keyword_density / 100))
        
        # Count current keyword occurrences
        current_occurrences = text.lower().count(target_keyword.lower())
        
        optimization = {
            'original_text': text,
            'word_count': total_words,
            'current_keyword_count': current_occurrences,
            'current_density': round((current_occurrences / total_words) * 100, 2),
            'target_occurrences': target_occurrences,
            'target_density': keyword_density,
            'recommendations': []
        }
        
        if current_occurrences < target_occurrences:
            optimization['recommendations'].append(
                f"Add {target_occurrences - current_occurrences} more instances of '{target_keyword}'"
            )
        elif current_occurrences > target_occurrences * 1.5:
            optimization['recommendations'].append(
                f"Reduce keyword usage by {current_occurrences - target_occurrences} instances to avoid over-optimization"
            )
        
        # Check for keyword in important positions
        if not text.lower().startswith(target_keyword.lower()):
            optimization['recommendations'].append(
                f"Consider starting the content with '{target_keyword}'"
            )
        
        # Suggest related keywords
        keyword_parts = target_keyword.split()
        if len(keyword_parts) > 1:
            optimization['related_keywords'] = [
                ' '.join(keyword_parts[:-1]),  # Remove last word
                ' '.join(keyword_parts[1:]),   # Remove first word
                f"{target_keyword} tips",
                f"best {target_keyword}",
                f"{target_keyword} guide"
            ]
        
        return optimization
    
    def bulk_generate_content(self, data_file, content_type='product'):
        """Generate content for multiple items from CSV"""
        results = []
        
        with open(data_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                if content_type == 'product':
                    content = self.generate_product_content(row)
                elif content_type == 'blog':
                    content = self.generate_blog_content(row)
                
                content['source_data'] = row
                results.append(content)
        
        # Save results
        output_file = f"generated_content_{content_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as file:
            json.dump(results, file, indent=2, ensure_ascii=False)
        
        print(f"Generated {len(results)} {content_type} content pieces saved to {output_file}")
        return results

# Usage example
if __name__ == "__main__":
    generator = ContentGenerator()
    
    # Example product data
    product_data = {
        'brand': 'PetShop AL',
        'product_name': 'Premium Dog Food',
        'category': 'Pet Food',
        'main_keyword': 'premium dog food',
        'target_audience': 'dog owners',
        'key_benefit': 'superior nutrition',
        'feature_1': 'natural ingredients',
        'feature_2': 'balanced nutrition',
        'use_case': 'daily feeding'
    }
    
    # Generate product content
    product_content = generator.generate_product_content(product_data)
    print("Generated Product Content:")
    print(f"Title: {product_content['title']}")
    print(f"Meta Description: {product_content['meta_description']}")
    print(f"Description: {product_content['description']}")
    
    # Example blog topic
    blog_data = {
        'main_topic': 'dog training',
        'target_audience': 'new dog owners',
        'action': 'train your dog',
        'subtopic_1': 'basic commands',
        'subtopic_2': 'house training',
        'subtopic_3': 'socialization',
        'goal': 'have a well-behaved dog',
        'benefit': 'better relationship with your pet'
    }
    
    # Generate blog content
    blog_content = generator.generate_blog_content(blog_data)
    print(f"\nGenerated Blog Content:")
    print(f"Title: {blog_content['title']}")
    print(f"Meta Description: {blog_content['meta_description']}")
