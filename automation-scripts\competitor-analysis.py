#!/usr/bin/env python3
"""
Automated Competitor Analysis Tool
Analyzes competitor websites for SEO, content, and technical insights
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
from urllib.parse import urljoin, urlparse
import time
import re
from datetime import datetime
import concurrent.futures

class CompetitorAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def analyze_competitor(self, url):
        """Complete competitor analysis"""
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        analysis = {
            'url': url,
            'domain': urlparse(url).netloc,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'success'
        }
        
        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                analysis.update(self._analyze_content_strategy(soup))
                analysis.update(self._analyze_seo_strategy(soup))
                analysis.update(self._analyze_technical_setup(response, soup))
                analysis.update(self._analyze_social_presence(soup))
                analysis.update(self._analyze_monetization(soup))
                
        except Exception as e:
            analysis['status'] = 'error'
            analysis['error'] = str(e)
            
        return analysis
    
    def _analyze_content_strategy(self, soup):
        """Analyze content strategy"""
        content = {}
        
        # Page structure
        content['total_words'] = len(soup.get_text().split())
        content['paragraphs'] = len(soup.find_all('p'))
        content['lists'] = len(soup.find_all(['ul', 'ol']))
        content['tables'] = len(soup.find_all('table'))
        
        # Heading structure
        headings = {}
        for i in range(1, 7):
            headings[f'h{i}'] = len(soup.find_all(f'h{i}'))
        content['headings'] = headings
        
        # Content types
        content['images'] = len(soup.find_all('img'))
        content['videos'] = len(soup.find_all(['video', 'iframe']))
        content['forms'] = len(soup.find_all('form'))
        
        # Navigation analysis
        nav_elements = soup.find_all(['nav', 'menu'])
        nav_links = []
        for nav in nav_elements:
            links = nav.find_all('a', href=True)
            nav_links.extend([link.get_text().strip() for link in links if link.get_text().strip()])
        
        content['navigation_items'] = list(set(nav_links))
        content['navigation_count'] = len(content['navigation_items'])
        
        # Call-to-action analysis
        cta_keywords = ['buy', 'shop', 'order', 'purchase', 'get', 'download', 'subscribe', 'sign up', 'contact']
        cta_elements = []
        
        for keyword in cta_keywords:
            buttons = soup.find_all(['button', 'a'], string=re.compile(keyword, re.I))
            cta_elements.extend([btn.get_text().strip() for btn in buttons])
        
        content['cta_elements'] = list(set(cta_elements))
        content['cta_count'] = len(content['cta_elements'])
        
        return {'content_strategy': content}
    
    def _analyze_seo_strategy(self, soup):
        """Analyze SEO strategy"""
        seo = {}
        
        # Basic SEO elements
        title = soup.find('title')
        seo['title'] = title.get_text().strip() if title else ''
        seo['title_length'] = len(seo['title'])
        
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        seo['meta_description'] = meta_desc.get('content', '') if meta_desc else ''
        seo['meta_desc_length'] = len(seo['meta_description'])
        
        # Keywords analysis
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        seo['meta_keywords'] = meta_keywords.get('content', '') if meta_keywords else ''
        
        # Extract potential target keywords from title and headings
        text_content = seo['title'] + ' '
        for heading in soup.find_all(['h1', 'h2', 'h3']):
            text_content += heading.get_text() + ' '
        
        # Simple keyword extraction (words appearing multiple times)
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text_content.lower())
        word_freq = {}
        for word in words:
            if word not in ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will']:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get top keywords
        top_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        seo['potential_keywords'] = [kw[0] for kw in top_keywords]
        
        # Internal linking
        internal_links = soup.find_all('a', href=True)
        seo['internal_links'] = len([link for link in internal_links if not self._is_external_link(link.get('href'))])
        seo['external_links'] = len([link for link in internal_links if self._is_external_link(link.get('href'))])
        
        # Image optimization
        images = soup.find_all('img')
        seo['images_with_alt'] = len([img for img in images if img.get('alt')])
        seo['images_without_alt'] = len(images) - seo['images_with_alt']
        
        return {'seo_strategy': seo}
    
    def _analyze_technical_setup(self, response, soup):
        """Analyze technical setup"""
        tech = {}
        
        # Performance indicators
        tech['response_size_kb'] = round(len(response.content) / 1024, 2)
        tech['https'] = response.url.startswith('https://')
        
        # Meta tags
        viewport = soup.find('meta', attrs={'name': 'viewport'})
        tech['mobile_optimized'] = bool(viewport)
        
        canonical = soup.find('link', rel='canonical')
        tech['canonical_tag'] = bool(canonical)
        
        # Structured data
        json_ld = soup.find_all('script', type='application/ld+json')
        tech['structured_data'] = len(json_ld) > 0
        tech['structured_data_count'] = len(json_ld)
        
        # Social meta tags
        og_tags = soup.find_all('meta', attrs={'property': lambda x: x and x.startswith('og:')})
        twitter_tags = soup.find_all('meta', attrs={'name': lambda x: x and x.startswith('twitter:')})
        
        tech['open_graph_tags'] = len(og_tags)
        tech['twitter_tags'] = len(twitter_tags)
        
        # Analytics and tracking
        analytics_scripts = []
        scripts = soup.find_all('script')
        for script in scripts:
            script_content = script.get_text() if script.get_text() else script.get('src', '')
            if 'google-analytics' in script_content or 'gtag' in script_content:
                analytics_scripts.append('Google Analytics')
            elif 'facebook' in script_content or 'fbevents' in script_content:
                analytics_scripts.append('Facebook Pixel')
            elif 'hotjar' in script_content:
                analytics_scripts.append('Hotjar')
        
        tech['tracking_tools'] = list(set(analytics_scripts))
        
        return {'technical_setup': tech}
    
    def _analyze_social_presence(self, soup):
        """Analyze social media presence"""
        social = {}
        
        # Social media links
        social_platforms = {
            'facebook': ['facebook.com', 'fb.com'],
            'twitter': ['twitter.com', 'x.com'],
            'instagram': ['instagram.com'],
            'linkedin': ['linkedin.com'],
            'youtube': ['youtube.com', 'youtu.be'],
            'tiktok': ['tiktok.com'],
            'pinterest': ['pinterest.com']
        }
        
        found_platforms = {}
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link.get('href', '').lower()
            for platform, domains in social_platforms.items():
                if any(domain in href for domain in domains):
                    found_platforms[platform] = href
                    break
        
        social['social_links'] = found_platforms
        social['social_platforms_count'] = len(found_platforms)
        
        # Social sharing buttons
        share_indicators = ['share', 'tweet', 'like', 'follow']
        share_buttons = []
        
        for indicator in share_indicators:
            buttons = soup.find_all(['a', 'button'], class_=re.compile(indicator, re.I))
            share_buttons.extend([btn.get('class', []) for btn in buttons])
        
        social['has_social_sharing'] = len(share_buttons) > 0
        
        return {'social_presence': social}
    
    def _analyze_monetization(self, soup):
        """Analyze monetization strategies"""
        monetization = {}
        
        # E-commerce indicators
        ecommerce_indicators = [
            'add to cart', 'buy now', 'checkout', 'shopping cart', 'price', '$', '€', '£'
        ]
        
        page_text = soup.get_text().lower()
        ecommerce_signals = sum(1 for indicator in ecommerce_indicators if indicator in page_text)
        monetization['ecommerce_signals'] = ecommerce_signals
        monetization['likely_ecommerce'] = ecommerce_signals >= 3
        
        # Forms (lead generation)
        forms = soup.find_all('form')
        email_inputs = soup.find_all('input', type='email')
        monetization['forms_count'] = len(forms)
        monetization['email_capture'] = len(email_inputs) > 0
        
        # Advertising
        ad_indicators = ['advertisement', 'sponsored', 'ads by']
        ad_signals = sum(1 for indicator in ad_indicators if indicator in page_text)
        monetization['advertising_signals'] = ad_signals
        
        return {'monetization': monetization}
    
    def _is_external_link(self, href):
        """Check if link is external"""
        if not href or href.startswith(('#', 'mailto:', 'tel:', '/')):
            return False
        return href.startswith(('http://', 'https://'))
    
    def bulk_analyze_competitors(self, competitor_urls, output_file='competitor_analysis.json'):
        """Analyze multiple competitors"""
        results = []
        
        print(f"Analyzing {len(competitor_urls)} competitors...")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            future_to_url = {executor.submit(self.analyze_competitor, url): url for url in competitor_urls}
            
            for i, future in enumerate(concurrent.futures.as_completed(future_to_url), 1):
                url = future_to_url[future]
                try:
                    result = future.result()
                    results.append(result)
                    print(f"Completed {i}/{len(competitor_urls)}: {url}")
                except Exception as e:
                    print(f"Error analyzing {url}: {e}")
                
                # Be respectful
                time.sleep(2)
        
        # Save results
        with open(output_file, 'w', encoding='utf-8') as file:
            json.dump(results, file, indent=2, ensure_ascii=False)
        
        print(f"Analysis complete! Results saved to {output_file}")
        
        # Generate summary report
        self._generate_summary_report(results)
        
        return results
    
    def _generate_summary_report(self, results):
        """Generate a summary report"""
        if not results:
            return
        
        summary = {
            'total_analyzed': len(results),
            'successful_analyses': len([r for r in results if r.get('status') == 'success']),
            'average_metrics': {},
            'common_strategies': {}
        }
        
        successful_results = [r for r in results if r.get('status') == 'success']
        
        if successful_results:
            # Calculate averages
            metrics = ['content_strategy', 'seo_strategy', 'technical_setup']
            for metric in metrics:
                if metric in successful_results[0]:
                    summary['average_metrics'][metric] = {}
                    
            # Common social platforms
            all_platforms = []
            for result in successful_results:
                if 'social_presence' in result and 'social_links' in result['social_presence']:
                    all_platforms.extend(result['social_presence']['social_links'].keys())
            
            platform_counts = {}
            for platform in all_platforms:
                platform_counts[platform] = platform_counts.get(platform, 0) + 1
            
            summary['common_strategies']['social_platforms'] = platform_counts
        
        # Save summary
        with open('competitor_summary.json', 'w', encoding='utf-8') as file:
            json.dump(summary, file, indent=2, ensure_ascii=False)
        
        print("Summary report saved to competitor_summary.json")

# Usage example
if __name__ == "__main__":
    analyzer = CompetitorAnalyzer()
    
    # Example competitor URLs
    competitors = [
        "petco.com",
        "petsmart.com",
        "chewy.com"
    ]
    
    results = analyzer.bulk_analyze_competitors(competitors)
    
    # Print key insights
    print("\nKey Insights:")
    for result in results:
        if result.get('status') == 'success':
            domain = result['domain']
            seo = result.get('seo_strategy', {})
            content = result.get('content_strategy', {})
            
            print(f"\n{domain}:")
            print(f"  - Title: {seo.get('title', 'N/A')[:50]}...")
            print(f"  - Word count: {content.get('total_words', 'N/A')}")
            print(f"  - Navigation items: {content.get('navigation_count', 'N/A')}")
            print(f"  - CTA count: {content.get('cta_count', 'N/A')}")
